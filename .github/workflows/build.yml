name: Build

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Setup JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      # This is needed to be able to run ./gradlew below
      # You can run `git update-index --chmod +x gradlew` then remove this step.
      - name: Make Gradle wrapper executable
        run: chmod +x ./gradlew

      - name: Build with Gradle
        run: ./gradlew build