package de.sarocesch.sarosmoneymod;

import de.sarocesch.sarosmoneymod.data.BalanceMigrator;
import de.sarocesch.sarosmoneymod.data.SignAttachmentType;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModBlocks;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModItems;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModTabs;
import de.sarocesch.sarosmoneymod.listener.SignShopListener;
import de.sarocesch.sarosmoneymod.network.NetworkHandler;
import de.sarocesch.sarosmoneymod.villager.ModVillagers;
import net.minecraft.server.MinecraftServer;
import net.neoforged.neoforge.common.NeoForge;
import net.neoforged.neoforge.event.server.ServerStartingEvent;
import net.neoforged.neoforge.event.tick.ServerTickEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import net.minecraft.resources.ResourceLocation;
import net.neoforged.bus.api.IEventBus;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.ModLoadingContext;
import net.neoforged.fml.common.Mod;
import net.neoforged.fml.config.ModConfig;
import net.neoforged.fml.event.lifecycle.FMLCommonSetupEvent;
import net.neoforged.fml.javafmlmod.FMLJavaModLoadingContext;

import de.sarocesch.sarosmoneymod.init.SarosMoneyModModMenus;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModBlockEntities;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.function.Supplier;
import java.util.function.Function;
import java.util.function.BiConsumer;

@Mod(SarosMoneyModMod.MODID)
public class SarosMoneyModMod {
	public static final Logger LOGGER = LogManager.getLogger(SarosMoneyModMod.class);
	public static final String MODID = "saros__money_mod";

	public SarosMoneyModMod() {
		NeoForge.EVENT_BUS.register(this);

		IEventBus bus = FMLJavaModLoadingContext.get().getModEventBus();
		NeoForge.EVENT_BUS.register(new SignShopListener());

		SarosMoneyModModBlocks.REGISTRY.register(bus);
		SarosMoneyModModItems.REGISTRY.register(bus);
		SarosMoneyModModTabs.REGISTRY.register(bus);

		SarosMoneyModModBlockEntities.REGISTRY.register(bus);
		ModVillagers.register(bus);
		SarosMoneyModModMenus.REGISTRY.register(bus);

		bus.addListener(this::commonSetup);
	}

	@SubscribeEvent
	public static void serverLoad(ServerStartingEvent event) {
		MinecraftServer server = event.getServer();
		BalanceMigrator.migrateOldData(server);
	}

	private void commonSetup(FMLCommonSetupEvent event) {
		event.enqueueWork(NetworkHandler::register);
	}

	private static final Collection<AbstractMap.SimpleEntry<Runnable, Integer>> workQueue = new ConcurrentLinkedQueue<>();

	public static void queueServerWork(int tick, Runnable action) {
		workQueue.add(new AbstractMap.SimpleEntry<>(action, tick));
	}

	@SubscribeEvent
	public void tick(ServerTickEvent.Post event) {
		List<AbstractMap.SimpleEntry<Runnable, Integer>> actions = new ArrayList<>();
		workQueue.forEach(work -> {
			work.setValue(work.getValue() - 1);
			if (work.getValue() == 0)
				actions.add(work);
		});
		actions.forEach(e -> e.getKey().run());
		workQueue.removeAll(actions);
	}
}