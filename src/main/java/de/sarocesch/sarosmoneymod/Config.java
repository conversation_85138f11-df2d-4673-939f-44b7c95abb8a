package de.sarocesch.sarosmoneymod;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.GsonBuilder;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.event.lifecycle.FMLCommonSetupEvent;
import net.neoforged.fml.loading.FMLPaths;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Mod.EventBusSubscriber(bus = Mod.EventBusSubscriber.Bus.MOD)
public class Config {
    private static final Logger LOGGER = LogManager.getLogger(Config.class);
    // General Money Config
    public static int START_MONEY = 100;  // Default value
    public static int MONEY_LOSS_ON_DEATH = 0;  // New default value for money loss on death
    public static boolean PAYCHECK_ENABLED = false; // Default for paycheck system
    public static int PAYCHECK_AMOUNT = 50;  // Default paycheck amount
    
    // Villager Config
    public static boolean VILLAGER_TRADES_ENABLED = true; // Default for villager trades enabled
    
    // Villager Trade Item Amounts
    public static int TRADE_L1_RAW_IRON_AMOUNT = 5; // Level 1 trade - raw iron amount
    public static int TRADE_L2_DIAMOND_AMOUNT = 3; // Level 2 trade - diamond amount
    public static int TRADE_L3_RAW_COPPER_AMOUNT = 5; // Level 3 trade - raw copper amount
    public static int TRADE_L4_RAW_IRON_AMOUNT = 3; // Level 4 trade - raw iron amount
    public static int TRADE_L4_DIAMOND_AMOUNT = 2; // Level 4 trade - diamond amount
    public static int TRADE_L5_GOLDEN_APPLE_AMOUNT = 1; // Level 5 trade - enchanted golden apple amount
    public static int TRADE_L5_NETHER_STAR_AMOUNT = 1; // Level 5 trade - nether star amount

    @SubscribeEvent
    public static void init(FMLCommonSetupEvent event) {
        loadConfig();
    }

    public static void loadConfig() {
        File configFile = new File(FMLPaths.GAMEDIR.get().toString() + "/config/", "saros_money_mod.json");

        if (!configFile.exists()) {
            createDefaultConfig(configFile);
        } else {
            try (FileReader reader = new FileReader(configFile)) {
                JsonObject json = JsonParser.parseReader(reader).getAsJsonObject();
                START_MONEY = json.has("startMoney") ? json.get("startMoney").getAsInt() : 1000;
                MONEY_LOSS_ON_DEATH = json.has("moneyLossOnDeath") ? json.get("moneyLossOnDeath").getAsInt() : 0;
                PAYCHECK_ENABLED = json.has("paycheckEnabled") ? json.get("paycheckEnabled").getAsBoolean() : false;
                PAYCHECK_AMOUNT = json.has("paycheckAmount") ? json.get("paycheckAmount").getAsInt() : 50;
                
                // Load Villager Config
                if (json.has("villagerConfig")) {
                    JsonObject villagerConfig = json.getAsJsonObject("villagerConfig");
                    VILLAGER_TRADES_ENABLED = villagerConfig.has("tradesEnabled") ? 
                            villagerConfig.get("tradesEnabled").getAsBoolean() : true;
                    
                    // Load trade item amounts
                    TRADE_L1_RAW_IRON_AMOUNT = villagerConfig.has("tradeL1RawIronAmount") ?
                            villagerConfig.get("tradeL1RawIronAmount").getAsInt() : 5;
                    TRADE_L2_DIAMOND_AMOUNT = villagerConfig.has("tradeL2DiamondAmount") ?
                            villagerConfig.get("tradeL2DiamondAmount").getAsInt() : 3;
                    TRADE_L3_RAW_COPPER_AMOUNT = villagerConfig.has("tradeL3RawCopperAmount") ?
                            villagerConfig.get("tradeL3RawCopperAmount").getAsInt() : 5;
                    TRADE_L4_RAW_IRON_AMOUNT = villagerConfig.has("tradeL4RawIronAmount") ?
                            villagerConfig.get("tradeL4RawIronAmount").getAsInt() : 3;
                    TRADE_L4_DIAMOND_AMOUNT = villagerConfig.has("tradeL4DiamondAmount") ?
                            villagerConfig.get("tradeL4DiamondAmount").getAsInt() : 2;
                    TRADE_L5_GOLDEN_APPLE_AMOUNT = villagerConfig.has("tradeL5GoldenAppleAmount") ?
                            villagerConfig.get("tradeL5GoldenAppleAmount").getAsInt() : 1;
                    TRADE_L5_NETHER_STAR_AMOUNT = villagerConfig.has("tradeL5NetherStarAmount") ?
                            villagerConfig.get("tradeL5NetherStarAmount").getAsInt() : 1;
                }
                
                // Check if config needs to be updated with new fields
                boolean configUpdated = updateConfigIfNeeded(json, configFile);
                if (configUpdated) {
                    LOGGER.info("Config file updated with new values");
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * Updates the config file with any new fields that were added in newer versions
     * while preserving existing values.
     * 
     * @param existingConfig The existing JSON configuration
     * @param configFile The config file to update
     * @return true if the config was updated, false otherwise
     */
    private static boolean updateConfigIfNeeded(JsonObject existingConfig, File configFile) {
        boolean configUpdated = false;
        
        // Check main config properties
        if (!existingConfig.has("startMoney")) {
            existingConfig.addProperty("startMoney", START_MONEY);
            configUpdated = true;
        }
        if (!existingConfig.has("moneyLossOnDeath")) {
            existingConfig.addProperty("moneyLossOnDeath", MONEY_LOSS_ON_DEATH);
            configUpdated = true;
        }
        if (!existingConfig.has("paycheckEnabled")) {
            existingConfig.addProperty("paycheckEnabled", PAYCHECK_ENABLED);
            configUpdated = true;
        }
        if (!existingConfig.has("paycheckAmount")) {
            existingConfig.addProperty("paycheckAmount", PAYCHECK_AMOUNT);
            configUpdated = true;
        }
        
        // Check villager config section
        JsonObject villagerConfig;
        if (!existingConfig.has("villagerConfig")) {
            villagerConfig = new JsonObject();
            existingConfig.add("villagerConfig", villagerConfig);
            configUpdated = true;
        } else {
            villagerConfig = existingConfig.getAsJsonObject("villagerConfig");
        }
        
        // Check villager config properties
        if (!villagerConfig.has("tradesEnabled")) {
            villagerConfig.addProperty("tradesEnabled", VILLAGER_TRADES_ENABLED);
            configUpdated = true;
        }
        
        // Check villager trade item amounts
        if (!villagerConfig.has("tradeL1RawIronAmount")) {
            villagerConfig.addProperty("tradeL1RawIronAmount", TRADE_L1_RAW_IRON_AMOUNT);
            configUpdated = true;
        }
        if (!villagerConfig.has("tradeL2DiamondAmount")) {
            villagerConfig.addProperty("tradeL2DiamondAmount", TRADE_L2_DIAMOND_AMOUNT);
            configUpdated = true;
        }
        if (!villagerConfig.has("tradeL3RawCopperAmount")) {
            villagerConfig.addProperty("tradeL3RawCopperAmount", TRADE_L3_RAW_COPPER_AMOUNT);
            configUpdated = true;
        }
        if (!villagerConfig.has("tradeL4RawIronAmount")) {
            villagerConfig.addProperty("tradeL4RawIronAmount", TRADE_L4_RAW_IRON_AMOUNT);
            configUpdated = true;
        }
        if (!villagerConfig.has("tradeL4DiamondAmount")) {
            villagerConfig.addProperty("tradeL4DiamondAmount", TRADE_L4_DIAMOND_AMOUNT);
            configUpdated = true;
        }
        if (!villagerConfig.has("tradeL5GoldenAppleAmount")) {
            villagerConfig.addProperty("tradeL5GoldenAppleAmount", TRADE_L5_GOLDEN_APPLE_AMOUNT);
            configUpdated = true;
        }
        if (!villagerConfig.has("tradeL5NetherStarAmount")) {
            villagerConfig.addProperty("tradeL5NetherStarAmount", TRADE_L5_NETHER_STAR_AMOUNT);
            configUpdated = true;
        }
        
        // If any updates were made, save the config file
        if (configUpdated) {
            try {
                Gson gson = new GsonBuilder().setPrettyPrinting().create();
                try (FileWriter fileWriter = new FileWriter(configFile)) {
                    fileWriter.write(gson.toJson(existingConfig));
                }
            } catch (IOException exception) {
                exception.printStackTrace();
                return false;
            }
        }
        
        return configUpdated;
    }

    private static void createDefaultConfig(File configFile) {
        try {
            configFile.getParentFile().mkdirs();
            configFile.createNewFile();
            JsonObject main = new JsonObject();
            main.addProperty("startMoney", START_MONEY);
            main.addProperty("moneyLossOnDeath", MONEY_LOSS_ON_DEATH);
            main.addProperty("paycheckEnabled", PAYCHECK_ENABLED);
            main.addProperty("paycheckAmount", PAYCHECK_AMOUNT);
            
            // Add Villager Config section
            JsonObject villagerConfig = new JsonObject();
            villagerConfig.addProperty("tradesEnabled", VILLAGER_TRADES_ENABLED);
            
            // Add villager trade item amounts
            villagerConfig.addProperty("tradeL1RawIronAmount", TRADE_L1_RAW_IRON_AMOUNT);
            villagerConfig.addProperty("tradeL2DiamondAmount", TRADE_L2_DIAMOND_AMOUNT);
            villagerConfig.addProperty("tradeL3RawCopperAmount", TRADE_L3_RAW_COPPER_AMOUNT);
            villagerConfig.addProperty("tradeL4RawIronAmount", TRADE_L4_RAW_IRON_AMOUNT);
            villagerConfig.addProperty("tradeL4DiamondAmount", TRADE_L4_DIAMOND_AMOUNT);
            villagerConfig.addProperty("tradeL5GoldenAppleAmount", TRADE_L5_GOLDEN_APPLE_AMOUNT);
            villagerConfig.addProperty("tradeL5NetherStarAmount", TRADE_L5_NETHER_STAR_AMOUNT);
            
            main.add("villagerConfig", villagerConfig);
            Gson gson = new GsonBuilder().setPrettyPrinting().create();
            try (FileWriter fileWriter = new FileWriter(configFile)) {
                fileWriter.write(gson.toJson(main));
            }
        } catch (IOException exception) {
            exception.printStackTrace();
        }
    }
}
